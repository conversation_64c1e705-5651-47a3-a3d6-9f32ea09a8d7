//
//  BottomSheetView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI

struct BottomSheetView: View {
    @Binding var isExpanded: Bool
    @State private var dragOffset: CGFloat = 0
    @State private var searchText: String = ""
    
    // Sheet height configurations
    private let collapsedHeight: CGFloat = 120
    private let expandedHeight: CGFloat = UIScreen.main.bounds.height * 0.5
    private let dragThreshold: CGFloat = 50
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Handle bar
                RoundedRectangle(cornerRadius: 3)
                    .fill(Color.gray.opacity(0.4))
                    .frame(width: 40, height: 6)
                    .padding(.top, 8)
                
                // Search section (always visible)
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.gray)
                            .font(.system(size: 18))
                        
                        TextField("搜索停车位...", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                            .font(.system(size: 16))
                        
                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.gray)
                                    .font(.system(size: 16))
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    .padding(.horizontal, 16)
                    
                    // Quick action buttons
                    HStack(spacing: 12) {
                        QuickActionButton(icon: "car.fill", title: "附近停车", color: .blue)
                        QuickActionButton(icon: "clock.fill", title: "预约停车", color: .green)
                        QuickActionButton(icon: "star.fill", title: "收藏位置", color: .orange)
                    }
                    .padding(.horizontal, 16)
                }
                .padding(.top, 8)
                
                // Expandable content
                if isExpanded {
                    ScrollView {
                        VStack(spacing: 16) {
                            // Recent searches section
                            VStack(alignment: .leading, spacing: 12) {
                                HStack {
                                    Text("最近搜索")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                    Spacer()
                                    Button("清除") {
                                        // Clear recent searches
                                    }
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                }
                                .padding(.horizontal, 16)
                                
                                LazyVStack(spacing: 8) {
                                    ForEach(recentSearches, id: \.self) { search in
                                        RecentSearchRow(searchText: search)
                                    }
                                }
                            }
                            
                            // Nearby parking section
                            VStack(alignment: .leading, spacing: 12) {
                                Text("附近停车场")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .padding(.horizontal, 16)
                                
                                LazyVStack(spacing: 12) {
                                    ForEach(nearbyParkingLots, id: \.id) { lot in
                                        ParkingLotRow(parkingLot: lot)
                                    }
                                }
                            }
                        }
                        .padding(.top, 16)
                    }
                    .transition(.opacity.combined(with: .move(edge: .top)))
                }
                
                Spacer()
            }
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
            )
            .frame(height: isExpanded ? expandedHeight : collapsedHeight)
            .offset(y: dragOffset)
            .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isExpanded)
            .animation(.spring(response: 0.3, dampingFraction: 0.9), value: dragOffset)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        let translation = value.translation.y
                        
                        if isExpanded {
                            // When expanded, only allow downward drag
                            dragOffset = max(0, translation)
                        } else {
                            // When collapsed, only allow upward drag
                            dragOffset = min(0, translation)
                        }
                    }
                    .onEnded { value in
                        let translation = value.translation.y
                        let velocity = value.predictedEndTranslation.y
                        
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                            if isExpanded {
                                // Collapse if dragged down enough
                                if translation > dragThreshold || velocity > 500 {
                                    isExpanded = false
                                }
                            } else {
                                // Expand if dragged up enough
                                if translation < -dragThreshold || velocity < -500 {
                                    isExpanded = true
                                }
                            }
                            dragOffset = 0
                        }
                    }
            )
        }
    }
    
    // Sample data
    private var recentSearches: [String] {
        ["墨尔本中央商务区", "南岸区", "弗林德斯街车站", "皇冠赌场"]
    }
    
    private var nearbyParkingLots: [ParkingLot] {
        [
            ParkingLot(id: 1, name: "中央停车场", distance: "200m", price: "$8/小时", availability: "15个空位"),
            ParkingLot(id: 2, name: "皇冠停车楼", distance: "350m", price: "$12/小时", availability: "8个空位"),
            ParkingLot(id: 3, name: "南门停车场", distance: "500m", price: "$6/小时", availability: "23个空位"),
            ParkingLot(id: 4, name: "弗林德斯停车站", distance: "650m", price: "$10/小时", availability: "5个空位")
        ]
    }
}

// MARK: - Supporting Views

struct QuickActionButton: View {
    let icon: String
    let title: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct RecentSearchRow: View {
    let searchText: String
    
    var body: some View {
        HStack {
            Image(systemName: "clock")
                .foregroundColor(.gray)
                .font(.system(size: 16))
            
            Text(searchText)
                .font(.system(size: 15))
                .foregroundColor(.primary)
            
            Spacer()
            
            Button(action: {
                // Remove from recent searches
            }) {
                Image(systemName: "xmark")
                    .foregroundColor(.gray)
                    .font(.system(size: 14))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .padding(.horizontal, 16)
    }
}

struct ParkingLotRow: View {
    let parkingLot: ParkingLot
    
    var body: some View {
        HStack(spacing: 12) {
            // Parking icon
            Image(systemName: "car.fill")
                .font(.system(size: 20))
                .foregroundColor(.blue)
                .frame(width: 40, height: 40)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(parkingLot.name)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                HStack {
                    Text(parkingLot.distance)
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    Text("•")
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    Text(parkingLot.price)
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(parkingLot.availability)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.green)
                
                Button("导航") {
                    // Navigate to parking lot
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding(16)
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal, 16)
    }
}

// MARK: - Data Models

struct ParkingLot {
    let id: Int
    let name: String
    let distance: String
    let price: String
    let availability: String
}

#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        VStack {
            Spacer()
            BottomSheetView(isExpanded: .constant(false))
        }
    }
}
