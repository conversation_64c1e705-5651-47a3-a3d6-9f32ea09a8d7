//
//  ContentView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import MapKit

struct ContentView: View {
    @StateObject private var locationManager = LocationManager()
    @State private var showingLocationAlert = false
    @State private var isBottomSheetExpanded = false

    var body: some View {
        NavigationStack {
            ZStack {
                // Main Map View with custom user location
                MapViewRepresentable(locationManager: locationManager)
                    .ignoresSafeArea()
                    .onAppear {
                        // LocationManager will automatically start location updates in init
                        // This is just a fallback in case user manually requests location
                        if locationManager.authorizationStatus == .authorizedWhenInUse ||
                           locationManager.authorizationStatus == .authorizedAlways {
                            locationManager.requestLocation()
                        }
                    }

                // Top Navigation Bar
                VStack {
                    HStack {
                        Text("XYZ Melbourne Parking")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color(.systemBackground).opacity(0.9))
                            .clipShape(RoundedRectangle(cornerRadius: 12))

                        Spacer()

                        // Location Button
                        Button(action: {
                            locationManager.requestLocation()
                        }) {
                            Image(systemName: "location.fill")
                                .font(.title3)
                                .foregroundColor(.blue)
                        }
                        .padding(12)
                        .background(Color(.systemBackground))
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                    Spacer()
                }

                // Bottom Sheet
                VStack {
                    Spacer()
                    BottomSheetView(isExpanded: $isBottomSheetExpanded)
                }
            }
        }
        .alert("Location Access Required", isPresented: $showingLocationAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable location access in Settings to find parking near you.")
        }
        .onChange(of: locationManager.authorizationStatus) { status in
            if status == .denied || status == .restricted {
                showingLocationAlert = true
            }
        }
    }
}

#Preview {
    ContentView()
}

// MARK: - MapView UIViewRepresentable
struct MapViewRepresentable: UIViewRepresentable {
    @ObservedObject var locationManager: LocationManager

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.showsUserLocation = false // We'll handle this ourselves
        mapView.userTrackingMode = .none
        mapView.showsCompass = true
        mapView.showsScale = true

        // Register custom annotation view
        mapView.register(UserLocationAnnotationView.self,
                        forAnnotationViewWithReuseIdentifier: UserLocationAnnotationView.identifier)

        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Update map region
        if mapView.region.center.latitude != locationManager.region.center.latitude ||
           mapView.region.center.longitude != locationManager.region.center.longitude {
            mapView.setRegion(locationManager.region, animated: true)
        }

        // Update user location annotation
        if let userAnnotation = locationManager.userLocationAnnotation {
            if !mapView.annotations.contains(where: { $0 === userAnnotation }) {
                mapView.addAnnotation(userAnnotation)
            }

            // Update heading for existing annotation view
            if let annotationView = mapView.view(for: userAnnotation) as? UserLocationAnnotationView {
                annotationView.updateHeading(locationManager.userHeading)
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: MapViewRepresentable

        init(_ parent: MapViewRepresentable) {
            self.parent = parent
        }

        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            if annotation is UserLocationAnnotation {
                let annotationView = mapView.dequeueReusableAnnotationView(
                    withIdentifier: UserLocationAnnotationView.identifier,
                    for: annotation
                ) as! UserLocationAnnotationView

                // Update heading immediately
                annotationView.updateHeading(parent.locationManager.userHeading)

                return annotationView
            }
            return nil
        }

        func mapView(_ mapView: MKMapView, didAdd views: [MKAnnotationView]) {
            // Update heading for newly added user location annotation
            for view in views {
                if let userLocationView = view as? UserLocationAnnotationView {
                    userLocationView.updateHeading(parent.locationManager.userHeading)
                }
            }
        }
    }
}
