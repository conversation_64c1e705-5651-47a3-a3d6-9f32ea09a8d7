//
//  BottomSheetDemo.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//  演示底部抽屉组件的使用方法
//

import SwiftUI

struct BottomSheetDemo: View {
    @State private var isExpanded = false
    
    var body: some View {
        ZStack {
            // 背景地图模拟
            LinearGradient(
                gradient: Gradient(colors: [Color.blue.opacity(0.3), Color.green.opacity(0.3)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // 模拟地图内容
            VStack {
                Text("地图区域")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .shadow(radius: 2)
                
                Text("点击下方搜索栏或向上拖拽")
                    .font(.headline)
                    .foregroundColor(.white)
                    .shadow(radius: 1)
                
                Spacer()
            }
            .padding(.top, 100)
            
            // 底部抽屉
            VStack {
                Spacer()
                BottomSheetView(isExpanded: $isExpanded)
            }
        }
    }
}

#Preview {
    BottomSheetDemo()
}

/*
 底部抽屉组件使用说明：
 
 1. 基本集成：
    - 在您的视图中添加 @State private var isBottomSheetExpanded = false
    - 在 ZStack 中添加 BottomSheetView(isExpanded: $isBottomSheetExpanded)
 
 2. 功能特性：
    - 默认折叠状态：仅显示搜索栏和快捷操作按钮
    - 手势交互：向上拖拽展开，向下拖拽收起
    - 平滑动画：使用 Spring 动画提供自然的过渡效果
    - 响应式设计：自动适配不同设备尺寸
 
 3. 自定义选项：
    - 修改 collapsedHeight 调整折叠高度
    - 修改 expandedHeight 调整展开高度（当前为屏幕高度的50%）
    - 修改 dragThreshold 调整拖拽敏感度
    - 自定义搜索栏占位符文本
    - 添加或修改快捷操作按钮
 
 4. 内容区域：
    - 最近搜索列表
    - 附近停车场列表
    - 可滚动内容区域
    - 支持添加更多自定义内容
 
 5. 数据绑定：
    - 搜索文本通过 @State 管理
    - 停车场数据可以从外部数据源获取
    - 支持实时更新内容
 */
